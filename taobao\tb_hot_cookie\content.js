// 等待页面加载完成
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initCookieButton);
} else {
  initCookieButton();
}

function initCookieButton() {
  // 检查是否已经添加了按钮
  if (document.getElementById('taobao-cookie-copy-btn')) {
    return;
  }
  
  // 创建复制按钮
  const copyButton = createCopyButton();
  
  // 将按钮添加到页面
  addButtonToPage(copyButton);
  
  // 添加点击事件
  copyButton.addEventListener('click', handleCopyClick);
}

function createCopyButton() {
  const button = document.createElement('div');
  button.id = 'taobao-cookie-copy-btn';
  button.innerHTML = `
    <div class="cookie-btn-content">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
      </svg>
      <span>复制Cookie</span>
    </div>
  `;
  return button;
}

function addButtonToPage(button) {
  // 尝试多个位置添加按钮
  const targetSelectors = [
    '.header',
    '.nav',
    '.toolbar',
    'body'
  ];
  
  let added = false;
  for (const selector of targetSelectors) {
    const target = document.querySelector(selector);
    if (target && !added) {
      target.appendChild(button);
      added = true;
      break;
    }
  }
  
  // 如果没有找到合适的位置，直接添加到body
  if (!added) {
    document.body.appendChild(button);
  }
}

async function handleCopyClick() {
  const button = document.getElementById('taobao-cookie-copy-btn');
  const originalContent = button.innerHTML;

  try {
    // 显示加载状态
    button.innerHTML = `
      <div class="cookie-btn-content loading">
        <div class="spinner"></div>
        <span>获取中...</span>
      </div>
    `;

    // 直接获取当前页面的所有cookie
    const cookieString = getAllCookies();

    if (!cookieString) {
      throw new Error('未找到cookie');
    }

    // 复制到剪贴板
    await navigator.clipboard.writeText(cookieString);

    // 计算cookie数量
    const cookieCount = cookieString.split(';').filter(c => c.trim()).length;

    // 显示成功状态
    button.innerHTML = `
      <div class="cookie-btn-content success">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
        </svg>
        <span>已复制 (${cookieCount}个)</span>
      </div>
    `;

    // 2秒后恢复原状
    setTimeout(() => {
      button.innerHTML = originalContent;
    }, 2000);

  } catch (error) {
    console.error('复制cookie失败:', error);

    // 显示错误状态
    button.innerHTML = `
      <div class="cookie-btn-content error">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
        <span>复制失败</span>
      </div>
    `;

    // 2秒后恢复原状
    setTimeout(() => {
      button.innerHTML = originalContent;
    }, 2000);
  }
}

function getAllCookies() {
  // 直接获取当前页面的所有cookie
  const cookies = document.cookie;

  if (!cookies) {
    return '';
  }

  // 格式化cookie字符串，每个cookie一行
  const cookieArray = cookies.split(';').map(cookie => cookie.trim()).filter(cookie => cookie);

  // 返回格式化的cookie字符串
  return cookieArray.join('; ');
}
