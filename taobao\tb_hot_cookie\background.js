// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getCookies') {
    getAllTaobaoCookies().then(cookies => {
      sendResponse({ cookies: cookies });
    }).catch(error => {
      console.error('获取cookie失败:', error);
      sendResponse({ error: error.message });
    });
    return true; // 保持消息通道开放
  }
});

// 获取所有淘宝相关域名的cookies
async function getAllTaobaoCookies() {
  const domains = [
    '.taobao.com',
    '.tmall.com',
    '.alibaba.com',
    '.alipay.com',
    '.alicdn.com',
    '.tbcdn.cn'
  ];
  
  let allCookies = [];
  
  for (const domain of domains) {
    try {
      const cookies = await chrome.cookies.getAll({ domain: domain });
      allCookies = allCookies.concat(cookies);
    } catch (error) {
      console.warn(`获取域名 ${domain} 的cookie失败:`, error);
    }
  }
  
  return allCookies;
}

// 格式化cookies为字符串
function formatCookies(cookies) {
  return cookies.map(cookie => {
    return `${cookie.name}=${cookie.value}`;
  }).join('; ');
}
