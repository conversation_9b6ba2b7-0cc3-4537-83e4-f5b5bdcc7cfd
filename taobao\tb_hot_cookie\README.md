# 淘宝Cookie获取器 Chrome插件

这是一个Chrome V3插件，用于在hot.taobao.com页面上显示复制按钮，一键获取并复制所有淘宝相关域名的cookie。

## 功能特点

- 🚀 无需弹出页面，直接在hot.taobao.com上操作
- 📋 一键复制当前页面的所有cookie
- 🎯 直接从页面获取cookie，确保数据完整性
- 💫 美观的UI设计，带有加载和成功状态提示
- 📱 响应式设计，支持移动端
- ⚡ 无需后台脚本，更轻量高效

## 安装方法

1. 打开Chrome浏览器
2. 进入扩展程序管理页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本插件的文件夹

## 使用方法

1. 确保已登录淘宝账号
2. 访问 hot.taobao.com
3. 在页面右上角会出现橙色的"复制Cookie"按钮
4. 点击按钮即可复制所有淘宝相关的cookie到剪贴板
5. 复制的cookie会按域名分组显示

## 权限说明

- `activeTab`: 访问当前标签页

## 文件结构

```
tb_hot_cookie/
├── manifest.json      # 插件配置文件
├── content.js         # 内容脚本
├── styles.css         # 样式文件
└── README.md          # 说明文档
```

## 技术特点

- 使用Chrome Extension Manifest V3
- 纯内容脚本实现，无需后台脚本
- 直接从document.cookie获取数据
- 现代化的CSS样式和动画效果
- 轻量级设计，最小权限原则
