#taobao-cookie-copy-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  user-select: none;
}

#taobao-cookie-copy-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.4);
}

#taobao-cookie-copy-btn:active {
  transform: translateY(0);
}

.cookie-btn-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.cookie-btn-content.loading {
  color: #fff;
}

.cookie-btn-content.success {
  color: #fff;
}

.cookie-btn-content.error {
  color: #fff;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  #taobao-cookie-copy-btn {
    top: 10px;
    right: 10px;
    padding: 10px 12px;
  }
  
  .cookie-btn-content {
    font-size: 12px;
  }
}

/* 确保按钮在所有页面元素之上 */
#taobao-cookie-copy-btn {
  position: fixed !important;
  z-index: 2147483647 !important;
}
